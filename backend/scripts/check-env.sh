#!/bin/bash

# Environment Variable Checker for Railway Deployment
# This script helps diagnose missing environment variables

echo "🔍 Checking Railway Environment Variables..."
echo "=============================================="

# Required environment variables
REQUIRED_VARS=(
    "PORT"
    "DATABASE_URL"
    "REDIS_URL"
    "MINIO_ENDPOINT"
    "MINIO_ACCESS_KEY"
    "MINIO_SECRET_KEY"
    "MINIO_BUCKET_NAME"
    "MINIO_USE_SSL"
    "GIN_MODE"
    "FRONTEND_URL"
)

# Optional environment variables
OPTIONAL_VARS=(
    "JWT_SECRET"
    "CORS_ORIGINS"
    "LOG_LEVEL"
)

missing_vars=()
present_vars=()

echo "📋 Required Variables:"
echo "----------------------"

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ $var: NOT SET"
        missing_vars+=("$var")
    else
        echo "✅ $var: SET"
        present_vars+=("$var")
    fi
done

echo ""
echo "📋 Optional Variables:"
echo "----------------------"

for var in "${OPTIONAL_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "⚠️  $var: NOT SET (optional)"
    else
        echo "✅ $var: SET"
    fi
done

echo ""
echo "📊 Summary:"
echo "============"
echo "✅ Present: ${#present_vars[@]}/${#REQUIRED_VARS[@]} required variables"
echo "❌ Missing: ${#missing_vars[@]} required variables"

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "🚨 Missing Required Variables:"
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "💡 To fix this in Railway:"
    echo "   1. Go to your Railway project dashboard"
    echo "   2. Select your backend service"
    echo "   3. Go to Variables tab"
    echo "   4. Add the missing environment variables"
    echo ""
    exit 1
else
    echo ""
    echo "🎉 All required environment variables are set!"
    echo ""
    
    # Additional MinIO connectivity check
    echo "🔗 Testing MinIO connectivity..."
    if command -v curl &> /dev/null; then
        if [ "$MINIO_USE_SSL" = "true" ]; then
            MINIO_URL="https://$MINIO_ENDPOINT"
        else
            MINIO_URL="http://$MINIO_ENDPOINT"
        fi
        
        echo "   Testing: $MINIO_URL/minio/health/live"
        if curl -s --max-time 10 "$MINIO_URL/minio/health/live" > /dev/null; then
            echo "✅ MinIO endpoint is reachable"
        else
            echo "❌ MinIO endpoint is not reachable"
            echo "   This might be normal if MinIO is behind authentication"
        fi
    else
        echo "⚠️  curl not available, skipping connectivity test"
    fi
    
    exit 0
fi
