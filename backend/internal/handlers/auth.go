package handlers

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	db          *database.DB
	authService *services.AuthService
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *database.DB) (*AuthHandler, error) {
	authService, err := services.NewAuthService(db)
	if err != nil {
		return nil, err
	}

	return &AuthHandler{
		db:          db,
		authService: authService,
	}, nil
}

// LoginResponse represents the response after successful login
type LoginResponse struct {
	Token     string                 `json:"token"`
	ExpiresAt time.Time              `json:"expires_at"`
	User      map[string]interface{} `json:"user"`
}

// GoogleLogin initiates Google OAuth login
// @Summary Initiate Google OAuth login
// @Description Redirects to Google OAuth consent screen
// @Tags auth
// @Produce json
// @Success 302 {string} string "Redirect to Google OAuth"
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/auth/google/login [get]
func (h *AuthHandler) GoogleLogin(c *gin.Context) {
	// Generate state for CSRF protection
	state, err := h.authService.GenerateState()
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "state_generation_failed",
			Message: "Failed to generate OAuth state",
			Code:    http.StatusInternalServerError,
		})
		return
	}

	// Store state in session/cookie for validation
	c.SetCookie("oauth_state", state, 600, "/", "", false, true) // 10 minutes

	// Get authorization URL
	authURL := h.authService.GetAuthURL(state)

	// Redirect to Google OAuth
	c.Redirect(http.StatusFound, authURL)
}

// GoogleCallback handles Google OAuth callback
// @Summary Handle Google OAuth callback
// @Description Processes Google OAuth callback and returns JWT token
// @Tags auth
// @Produce json
// @Param code query string true "Authorization code from Google"
// @Param state query string true "State parameter for CSRF protection"
// @Success 200 {object} LoginResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/auth/google/callback [get]
func (h *AuthHandler) GoogleCallback(c *gin.Context) {
	// Verify state parameter
	state := c.Query("state")
	storedState, err := c.Cookie("oauth_state")
	if err != nil || state != storedState {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_state",
			Message: "Invalid or missing state parameter",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Clear the state cookie
	c.SetCookie("oauth_state", "", -1, "/", "", false, true)

	// Get authorization code
	code := c.Query("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "missing_code",
			Message: "Authorization code is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	ctx := context.Background()

	// Exchange code for token
	token, err := h.authService.ExchangeCodeForToken(ctx, code)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "token_exchange_failed",
			Message: "Failed to exchange authorization code for token",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	// Get user info from Google
	userInfo, err := h.authService.GetUserInfo(ctx, token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "user_info_failed",
			Message: "Failed to retrieve user information from Google",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Create or update user in database
	user, err := h.authService.CreateOrUpdateUser(ctx, userInfo)
	if err != nil {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error:   "user_not_authorized",
			Message: "User is not authorized to access this application",
			Code:    http.StatusUnauthorized,
			Details: err.Error(),
		})
		return
	}

	// Generate JWT token
	jwtToken, err := h.authService.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "jwt_generation_failed",
			Message: "Failed to generate authentication token",
			Code:    http.StatusInternalServerError,
		})
		return
	}

	// Get redirect URL from cookie
	redirectURL, _ := c.Cookie("oauth_redirect")

	// Clear OAuth cookies
	c.SetCookie("oauth_state", "", -1, "/", "", false, true)
	c.SetCookie("oauth_redirect", "", -1, "/", "", false, true)

	// Build frontend callback URL
	frontendURL := os.Getenv("FRONTEND_URL")
	if frontendURL == "" {
		frontendURL = "http://localhost:3000" // Default for development
	}

	// Construct callback URL with token
	callbackURL := fmt.Sprintf("%s/?token=%s", frontendURL, jwtToken)

	// Add redirect parameter if it was provided
	if redirectURL != "" {
		callbackURL += fmt.Sprintf("&redirect=%s", url.QueryEscape(redirectURL))
	}

	// Redirect to frontend
	c.Redirect(http.StatusFound, callbackURL)
}

// Logout handles user logout
// @Summary Logout user
// @Description Logout user (client-side token removal)
// @Tags auth
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// For JWT tokens, logout is primarily client-side
	// In the future, we could implement token blacklisting
	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
		"status":  "success",
	})
}

// Me returns current user information
// @Summary Get current user
// @Description Returns information about the currently authenticated user
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} middleware.ErrorResponse
// @Router /api/v1/auth/me [get]
func (h *AuthHandler) Me(c *gin.Context) {
	// Get user from context (set by auth middleware)
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error:   "user_not_found",
			Message: "User information not found in request context",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	user, ok := userInterface.(*services.JWTClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error:   "invalid_user_data",
			Message: "Invalid user data in request context",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": map[string]interface{}{
			"id":        user.UserID,
			"email":     user.Email,
			"name":      user.Name,
			"role":      user.Role,
			"is_active": user.IsActive,
		},
		"status": "authenticated",
	})
}

// RefreshToken refreshes the JWT token
// @Summary Refresh JWT token
// @Description Refreshes the current JWT token with a new expiry time
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} LoginResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Router /api/v1/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// Get user from context (set by auth middleware)
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error:   "user_not_found",
			Message: "User information not found in request context",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	claims, ok := userInterface.(*services.JWTClaims)
	if !ok {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error:   "invalid_user_data",
			Message: "Invalid user data in request context",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	// Create a user object from claims to generate new token
	user := &models.User{
		ID:       claims.UserID,
		Email:    claims.Email,
		Name:     claims.Name,
		Role:     claims.Role,
		IsActive: claims.IsActive,
	}

	// Generate new JWT token
	jwtToken, err := h.authService.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "jwt_generation_failed",
			Message: "Failed to generate new authentication token",
			Code:    http.StatusInternalServerError,
		})
		return
	}

	// Calculate expiry time
	expiresAt := time.Now().Add(24 * time.Hour)

	c.JSON(http.StatusOK, LoginResponse{
		Token:     jwtToken,
		ExpiresAt: expiresAt,
		User: map[string]interface{}{
			"id":        user.ID,
			"email":     user.Email,
			"name":      user.Name,
			"role":      user.Role,
			"is_active": user.IsActive,
		},
	})
}
