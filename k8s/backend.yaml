apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: jewelry-store
  labels:
    app: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/backend:2
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: PORT
        - name: GIN_MODE
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: GIN_MODE
        - name: DB_HOST
          value: postgres-service
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_DB
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_PASSWORD
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_USER
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secrets
              key: REDIS_PASSWORD
        - name: MINIO_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: MINIO_ENDPOINT
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_NAME
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: MINIO_BUCKET_NAME
        - name: FRONTEND_URL
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: FRONTEND_URL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: JWT_SECRET
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: jewelry-store
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
