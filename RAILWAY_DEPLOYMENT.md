# Railway Deployment Guide

This guide explains how to deploy the Anand Jewels application to Railway.

## Overview

The application consists of three main services:
- **Backend**: Go API server
- **Frontend**: React customer interface
- **Admin Portal**: React admin interface

Each service needs to be deployed as a separate Railway service.

## Prerequisites

1. Railway account
2. Railway CLI installed
3. Git repository connected to Railway

## Deployment Steps

### 1. Backend Service

Create a new Railway service for the backend:

```bash
# In Railway dashboard, create a new service
# Connect your GitHub repository
# Set the following configuration:
```

**Build Command:**
```bash
make railway-build-backend
```

**Start Command:**
```bash
make railway-start-backend
```

**Environment Variables:**
```
PORT=8080
DATABASE_URL=postgresql://username:password@host:port/database
REDIS_URL=redis://host:port
MINIO_ENDPOINT=your-minio-endpoint
MINIO_ACCESS_KEY=your-access-key
MINIO_SECRET_KEY=your-secret-key
MINIO_BUCKET_NAME=jewelry-images
MINIO_USE_SSL=true
API_BASE_URL=https://your-backend-service.railway.app
GIN_MODE=release
```

**Note:** The backend now supports both `MINIO_BUCKET_NAME` and `MINIO_BUCKET` for backward compatibility.

**Root Directory:** `backend`

### 2. Frontend Service

Create a new Railway service for the customer frontend:

**Build Command:**
```bash
make railway-build-frontend
```

**Start Command:**
```bash
make railway-start-frontend
```

**Environment Variables:**
```
VITE_API_URL=https://your-backend-service.railway.app/api/v1
VITE_APP_NAME=Anand Jewels
VITE_DEBUG_MODE=false
```

**Root Directory:** `frontend`

### 3. Admin Portal Service

Create a new Railway service for the admin portal:

**Build Command:**
```bash
make railway-build-admin
```

**Start Command:**
```bash
make railway-start-admin
```

**Environment Variables:**
```
VITE_API_URL=https://your-backend-service.railway.app/api/v1
VITE_APP_NAME=Anand Jewels Admin
VITE_DEBUG_MODE=false
```

**Root Directory:** `admin-portal`

## Alternative: Single Service Deployment

If you prefer to deploy all services from a single Railway service, you can use:

**Build Command:**
```bash
make railway-build
```

**Start Command:**
```bash
SERVICE=backend make railway-start
```

Then set the `SERVICE` environment variable to one of: `backend`, `frontend`, or `admin`.

## Database Setup

### PostgreSQL

1. Add a PostgreSQL plugin to your Railway project
2. Copy the connection string to your backend service's `DATABASE_URL` environment variable
3. The database will be automatically created and migrations will run on startup

### Redis

1. Add a Redis plugin to your Railway project
2. Copy the connection string to your backend service's `REDIS_URL` environment variable

## File Storage

For production, you'll need to set up external file storage:

### Option 1: AWS S3
```
MINIO_ENDPOINT=s3.amazonaws.com
MINIO_ACCESS_KEY=your-aws-access-key
MINIO_SECRET_KEY=your-aws-secret-key
MINIO_BUCKET_NAME=your-s3-bucket
MINIO_USE_SSL=true
```

### Option 2: MinIO Cloud
```
MINIO_ENDPOINT=your-minio-cloud-endpoint
MINIO_ACCESS_KEY=your-minio-access-key
MINIO_SECRET_KEY=your-minio-secret-key
MINIO_BUCKET_NAME=jewelry-images
MINIO_USE_SSL=true
```

## Custom Domains

1. In Railway dashboard, go to your service settings
2. Add your custom domain
3. Update CORS settings in backend if needed
4. Update frontend environment variables with new API URL

## Monitoring

Railway provides built-in monitoring. You can also add:

1. **Health Checks**: The backend includes `/health` endpoint
2. **Logs**: Available in Railway dashboard
3. **Metrics**: CPU, memory, and network usage in Railway dashboard

## Troubleshooting

### Build Failures

1. Check build logs in Railway dashboard
2. Ensure all dependencies are properly listed in package.json/go.mod
3. Verify build commands are correct

### Runtime Issues

1. Check application logs in Railway dashboard
2. Verify environment variables are set correctly
3. Test database and Redis connections
4. Check file storage configuration

### Common Issues

**Backend not starting:**
- Check `DATABASE_URL` format
- Verify Redis connection
- Ensure MinIO credentials are correct

**Frontend not loading:**
- Check `VITE_API_URL` points to correct backend
- Verify CORS settings in backend
- Check build output for errors

**Images not uploading:**
- Verify MinIO/S3 credentials
- Check bucket permissions
- Ensure CORS is configured for file storage

## Environment-Specific Configuration

### Development
```
GIN_MODE=debug
VITE_DEBUG_MODE=true
```

### Production
```
GIN_MODE=release
VITE_DEBUG_MODE=false
```

## Security Considerations

1. Use environment variables for all secrets
2. Enable HTTPS (Railway provides this automatically)
3. Configure CORS properly
4. Use secure database connections
5. Implement rate limiting if needed

## Scaling

Railway automatically handles scaling based on traffic. For high-traffic scenarios:

1. Consider using Railway's Pro plan
2. Optimize database queries
3. Implement caching strategies
4. Use CDN for static assets

## Backup Strategy

1. **Database**: Railway PostgreSQL includes automatic backups
2. **Files**: Ensure your file storage provider has backup/versioning
3. **Code**: Keep your Git repository as source of truth

## Support

For Railway-specific issues:
- Railway Documentation: https://docs.railway.app
- Railway Discord: https://discord.gg/railway
- Railway GitHub: https://github.com/railwayapp/railway

For application issues:
- Check application logs
- Review this deployment guide
- Verify environment configuration
